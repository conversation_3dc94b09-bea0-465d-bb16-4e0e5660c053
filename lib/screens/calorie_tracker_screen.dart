import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import '../models/user_profile.dart';
import '../models/fuzzy_logic.dart';
import '../models/food_database.dart';

class CalorieTrackerScreen extends StatefulWidget {
  const CalorieTrackerScreen({super.key});

  @override
  State<CalorieTrackerScreen> createState() => _CalorieTrackerScreenState();
}

class _CalorieTrackerScreenState extends State<CalorieTrackerScreen> {
  final List<CalorieEntry> _entries = [];
  int _totalCalories = 0;
  int _targetCalories = 2500; // Default target
  
  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }
  
  Future<void> _loadUserProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasProfile = prefs.getBool('has_profile') ?? false;
      
      if (hasProfile) {
        // Baca data profil
        final name = prefs.getString('name') ?? '';
        final age = prefs.getInt('age') ?? 0;
        final weight = prefs.getDouble('weight') ?? 0;
        final height = prefs.getDouble('height') ?? 0;
        final gender = prefs.getString('gender') ?? 'Pria';
        final activityLevel = prefs.getString('activity_level') ?? 'Sedang';
        final goal = prefs.getString('goal') ?? 'Bulking';
        
        // Buat objek UserProfile
        final userProfile = UserProfile(
          name: name,
          age: age,
          weight: weight,
          height: height,
          gender: gender,
          activityLevel: activityLevel,
          goal: goal,
        );
        
        // Gunakan FuzzyLogic untuk menghitung kebutuhan kalori
        final fuzzyLogic = FuzzyLogic();
        final calculatedCalories = fuzzyLogic.calculateCalories(userProfile);
        
        setState(() {
          _targetCalories = calculatedCalories;
        });
      }
    } catch (e) {
      debugPrint('Error loading profile: $e');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tracking Kalori'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildCalorieSummary(),
          const Divider(height: 1),
          Expanded(
            child: _entries.isEmpty
                ? _buildEmptyState()
                : _buildFoodList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddFoodDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCalorieSummary() {
    final remainingCalories = _targetCalories - _totalCalories;
    final isOverCalories = remainingCalories < 0;
    
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              'Ringkasan Kalori Hari Ini',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildCalorieInfo('Target', '$_targetCalories', Colors.blue),
                _buildCalorieInfo('Dikonsumsi', '$_totalCalories', Colors.green),
                _buildCalorieInfo(
                  'Sisa', 
                  '${remainingCalories.abs()}', 
                  isOverCalories ? Colors.red : Colors.orange,
                  prefix: isOverCalories ? '+' : '',
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: (_totalCalories / _targetCalories).clamp(0.0, 1.0),
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(
                _totalCalories > _targetCalories ? Colors.red : Colors.green,
              ),
              minHeight: 10,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalorieInfo(String label, String value, Color color, {String prefix = ''}) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 4),
        Text(
          '$prefix$value',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const Text('kcal', style: TextStyle(fontSize: 12)),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.no_food, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Belum ada makanan yang ditambahkan',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Tekan tombol + untuk menambahkan makanan',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildFoodList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _entries.length,
      itemBuilder: (context, index) {
        final entry = _entries[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            title: Text(entry.foodName),
            subtitle: Text('${entry.calories} kcal'),
            trailing: IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _removeFood(index),
            ),
          ),
        );
      },
    );
  }

  void _showAddFoodDialog() {
    final nameController = TextEditingController();
    final caloriesController = TextEditingController();
    final searchController = TextEditingController();
    String? selectedFood;
    int selectedCalories = 0;
    
    // Gunakan database makanan Indonesia
    final Map<String, int> foodDatabase = FoodDatabase.indonesianFoods;
    List<String> filteredFoods = foodDatabase.keys.toList();

    showDialog(
      context: context,
      barrierDismissible: false, // Mencegah dialog tertutup saat klik di luar
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Tambah Makanan'),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Search bar untuk mencari makanan
                  TextField(
                    controller: searchController,
                    autofocus: true,
                    decoration: const InputDecoration(
                      labelText: 'Cari Makanan',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.search),
                      hintText: 'Ketik nama makanan...',
                    ),
                    onChanged: (value) {
                      setState(() {
                        if (value.isEmpty) {
                          filteredFoods = foodDatabase.keys.toList();
                        } else {
                          filteredFoods = foodDatabase.keys
                              .where((food) => food.toLowerCase().contains(value.toLowerCase()))
                              .toList();
                        }
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Daftar makanan yang dapat di-scroll
                  Container(
                    constraints: const BoxConstraints(maxHeight: 200),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: filteredFoods.isEmpty
                        ? const Center(child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Text('Makanan tidak ditemukan'),
                          ))
                        : ListView.builder(
                            shrinkWrap: true,
                            itemCount: filteredFoods.length > 10 ? 10 : filteredFoods.length,
                            itemBuilder: (context, index) {
                              final food = filteredFoods[index];
                              final calories = foodDatabase[food]!;
                              return InkWell(
                                onTap: () {
                                  setState(() {
                                    selectedFood = food;
                                    selectedCalories = calories;
                                    nameController.text = food;
                                    caloriesController.text = calories.toString();
                                  });
                                },
                                child: Container(
                                  color: selectedFood == food 
                                      ? Colors.blue.withAlpha((0.1 * 255).round())
                                      : null,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          food,
                                          style: const TextStyle(fontWeight: FontWeight.bold),
                                        ),
                                        Text('$calories kcal'),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                  ),
                  
                  const Divider(height: 24),
                  
                  // Tampilkan makanan yang dipilih
                  if (selectedFood != null)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.withAlpha((0.1 * 255).round()),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.check_circle, color: Colors.green),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Dipilih: $selectedFood', 
                                  style: const TextStyle(fontWeight: FontWeight.bold)),
                                Text('$selectedCalories kcal'),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  
                  const SizedBox(height: 16),
                  const Text('Atau masukkan makanan manual:'),
                  const SizedBox(height: 16),
                  
                  // Input manual
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Nama Makanan',
                      border: OutlineInputBorder(),
                      hintText: 'Masukkan nama makanan',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: caloriesController,
                    decoration: const InputDecoration(
                      labelText: 'Kalori (kcal)',
                      border: OutlineInputBorder(),
                      hintText: 'Masukkan jumlah kalori',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Batal'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.isNotEmpty) {
                  _addFood(
                    nameController.text,
                    selectedFood != null ? selectedCalories : (int.tryParse(caloriesController.text) ?? 0),
                  );
                  Navigator.pop(context);
                } else {
                  // Tampilkan pesan error jika nama makanan kosong
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Nama makanan tidak boleh kosong'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('Tambah'),
            ),
          ],
        ),
      ),
    );
  }

  void _showSettingsDialog() {
    final targetController = TextEditingController(text: _targetCalories.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pengaturan Kalori'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: targetController,
              decoration: const InputDecoration(labelText: 'Target Kalori Harian'),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () {
              if (targetController.text.isNotEmpty) {
                setState(() {
                  _targetCalories = int.tryParse(targetController.text) ?? 2500;
                });
                Navigator.pop(context);
              }
            },
            child: const Text('Simpan'),
          ),
        ],
      ),
    );
  }

  void _addFood(String name, int calories) {
    setState(() {
      _entries.add(CalorieEntry(
        foodName: name,
        calories: calories,
      ));
      _updateCalorieTotal();
    });
  }

  void _removeFood(int index) {
    setState(() {
      _entries.removeAt(index);
      _updateCalorieTotal();
    });
  }

  void _updateCalorieTotal() {
    _totalCalories = 0;
    for (final entry in _entries) {
      _totalCalories += entry.calories;
    }
    _saveConsumedCalories();
  }

  Future<void> _saveConsumedCalories() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateFormat('dd/MM/yyyy').format(DateTime.now());
    await prefs.setInt('calories_$today', _totalCalories);
  }
}

class CalorieEntry {
  final String foodName;
  final int calories;

  CalorieEntry({
    required this.foodName,
    required this.calories,
  });
}
