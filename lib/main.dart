
import 'package:flutter/material.dart';
import 'screens/splash_screen.dart';
import 'screens/debug_overlay.dart';

void main() {
 
 
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Bodybuilder Calorie Control',
      theme: ThemeData(
        colorScheme: ColorScheme.light(
          primary: Color(0xFF1976D2),       // Biru utama
          secondary: Color(0xFF64B5F6),     // Biru muda
          tertiary: Color(0xFF81D4FA),      // Biru sangat muda
          surface: Colors.white,
          surfaceContainerHighest: Color(0xFFE3F2FD),
          surfaceContainer: Color(0xFFE3F2FD),
          surfaceContainerLow: Color(0xFFBBDEFB),
          onPrimary: Colors.white,
          onSecondary: Colors.white,
          onSurface: Color(0xFF0D47A1),
        ),
        scaffoldBackgroundColor: Colors.white,
        appBarTheme: AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: Color(0xFF1976D2),
          elevation: 0,
        ),
        cardTheme: CardTheme(
          color: Colors.white,
          elevation: 1,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: Color(0xFF1976D2),
            foregroundColor: Colors.white,
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: Color(0xFF1976D2),
          ),
        ),
        iconTheme: IconThemeData(
          color: Color(0xFF1976D2),
        ),
        useMaterial3: true,
        fontFamily: 'Roboto',
      ),
      home: DebugOverlay(
        child: const SplashScreen(),
      ),
      debugShowCheckedModeBanner: false,
    );
  }
}
