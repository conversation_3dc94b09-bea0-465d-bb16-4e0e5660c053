class FoodDatabase {
  static final Map<String, int> indonesianFoods = {
    // <PERSON><PERSON><PERSON>
    'Nasi <PERSON> (1 porsi/100g)': 130,
    '<PERSON><PERSON> (1 porsi/100g)': 110,
    '<PERSON><PERSON> (1 porsi/100g)': 185,
    '<PERSON><PERSON> (1 porsi/100g)': 180,
    '<PERSON><PERSON> (1 porsi/150g)': 267,
    'Lontong (1 potong)': 38,
    '<PERSON><PERSON><PERSON><PERSON> (1 buah)': 40,
    '<PERSON><PERSON><PERSON> (1 mangkok)': 220,
    
    // <PERSON><PERSON>
    '<PERSON><PERSON> (1 potong paha)': 250,
    '<PERSON><PERSON> (1 potong dada)': 195,
    'Rendang <PERSON> (1 porsi/100g)': 285,
    '<PERSON><PERSON> (5 tusuk)': 175,
    '<PERSON><PERSON> (5 tusuk)': 210,
    '<PERSON>ur Dadar (1 butir)': 90,
    '<PERSON><PERSON> Rebus (1 butir)': 70,
    'Telur Balado (1 butir)': 95,
    '<PERSON><PERSON><PERSON> (2 potong)': 160,
    '<PERSON><PERSON> (2 potong)': 115,
    '<PERSON><PERSON> (1 ekor sedang)': 150,
    '<PERSON><PERSON> (1 ekor sedang)': 200,
    '<PERSON><PERSON><PERSON> (1 buah)': 75,
    '<PERSON><PERSON><PERSON>agung (1 buah)': 80,
    '<PERSON>pal <PERSON>ging (1 potong)': 195,
    'Dendeng Balado (1 porsi)': 210,
    'Ayam Penyet (1 porsi)': 272,
    'Bebek Goreng (1 porsi)': 320,
    
    // Sayuran
    'Sayur Asem (1 mangkok)': 85,
    'Sayur Lodeh (1 mangkok)': 125,
    'Sayur Sop (1 mangkok)': 70,
    'Sayur Bayam (1 mangkok)': 50,
    'Sayur Kangkung (1 mangkok)': 45,
    'Sayur Bening (1 mangkok)': 40,
    'Gado-gado (1 porsi)': 230,
    'Pecel (1 porsi)': 210,
    'Urap (1 porsi)': 150,
    'Cap Cay (1 porsi)': 120,
    'Tumis Kangkung (1 porsi)': 80,
    'Tumis Tauge (1 porsi)': 65,
    
    // Makanan Berkuah
    'Soto Ayam (1 mangkok)': 220,
    'Soto Betawi (1 mangkok)': 330,
    'Soto Madura (1 mangkok)': 245,
    'Bakso (1 mangkok, 5 butir)': 300,
    'Mie Ayam (1 mangkok)': 380,
    'Mie Goreng (1 porsi)': 420,
    'Mie Rebus (1 porsi)': 350,
    'Rawon (1 mangkok)': 290,
    'Gulai Kambing (1 mangkok)': 310,
    'Tongseng (1 mangkok)': 285,
    'Sup Buntut (1 mangkok)': 345,
    'Ketoprak (1 porsi)': 265,
    'Laksa (1 mangkok)': 320,
    
    // Makanan Ringan/Jajanan
    'Pisang Goreng (1 buah)': 120,
    'Tape Goreng (1 buah)': 115,
    'Martabak Manis (1 potong)': 270,
    'Martabak Telur (1 potong)': 250,
    'Terang Bulan (1 potong)': 260,
    'Kue Putu (1 buah)': 70,
    'Kue Lupis (1 buah)': 95,
    'Onde-onde (1 buah)': 80,
    'Lemper (1 buah)': 110,
    'Risoles (1 buah)': 140,
    'Pastel (1 buah)': 155,
    'Batagor (1 porsi)': 230,
    'Siomay (1 porsi)': 210,
    'Pempek (1 porsi)': 265,
    'Cireng (5 buah)': 200,
    'Bakwan (1 buah)': 70,
    'Tahu Isi (1 buah)': 80,
    'Klepon (3 buah)': 105,
    
    // Buah-buahan
    'Pisang (1 buah sedang)': 105,
    'Apel (1 buah sedang)': 80,
    'Jeruk (1 buah sedang)': 45,
    'Mangga (1 buah sedang)': 135,
    'Pepaya (1 potong sedang)': 55,
    'Semangka (1 potong sedang)': 50,
    'Melon (1 potong sedang)': 45,
    'Nanas (1 potong sedang)': 40,
    'Rambutan (10 buah)': 65,
    'Duku (10 buah)': 60,
    'Salak (3 buah)': 75,
    'Jambu Biji (1 buah sedang)': 45,
    'Alpukat (1/2 buah)': 160,
    'Durian (3 biji)': 270,
    
    // Minuman
    'Es Teh Manis (1 gelas)': 90,
    'Teh Tawar (1 gelas)': 2,
    'Kopi Hitam (1 gelas)': 5,
    'Kopi Susu (1 gelas)': 70,
    'Es Jeruk (1 gelas)': 110,
    'Es Kelapa Muda (1 gelas)': 130,
    'Jus Alpukat (1 gelas)': 190,
    'Jus Mangga (1 gelas)': 135,
    'Jus Jambu (1 gelas)': 120,
    'Es Cincau (1 gelas)': 85,
    'Es Campur (1 gelas)': 210,
    'Es Doger (1 gelas)': 230,
    'Wedang Jahe (1 gelas)': 50,
    'Bandrek (1 gelas)': 120,
    'Bajigur (1 gelas)': 150,
    'Sekoteng (1 gelas)': 165,
    
    // Makanan Penutup/Dessert
    'Es Krim (1 scoop)': 140,
    'Pudding (1 cup)': 120,
    'Kolak Pisang (1 mangkok)': 190,
    'Bubur Sumsum (1 mangkok)': 165,
    'Bubur Kacang Hijau (1 mangkok)': 200,
    'Es Buah (1 mangkok)': 170,
    'Kue Lapis (1 potong)': 115,
    'Bika Ambon (1 potong)': 160,
    'Kue Serabi (1 buah)': 95,
    'Kue Cucur (1 buah)': 80,
    'Kue Nagasari (1 buah)': 110,
    'Kue Dadar Gulung (1 buah)': 120,
    'Kue Apem (1 buah)': 100,
    'Kue Pancong (1 buah)': 90,
    'Kue Cubit (5 buah)': 150,
  };
}